<?php
/**
 * 5G Smart VPN Admin Panel - Configuration
 * Database connection and system configuration
 */

// Include the main config file
require_once dirname(__DIR__, 2) . '/config.php';

// Admin Panel Configuration
define('ADMIN_PANEL_VERSION', '2.0.0');
define('ADMIN_PANEL_NAME', '5G Smart VPN Admin Panel');
define('ADMIN_PANEL_URL', 'http://192.168.0.106/Svpn5g/5GsmartvpnAdminPanel/admin_new/');

// File Upload Configuration
define('UPLOAD_PATH', dirname(__DIR__, 2) . '/uploads/');
define('UPLOAD_URL', '/Svpn5g/5GsmartvpnAdminPanel/uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);

// Pagination Configuration
define('ITEMS_PER_PAGE', 20);

// Security Configuration
define('SESSION_TIMEOUT', 3600); // 1 hour
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes

// Notification Configuration
define('FCM_SERVER_KEY', 'your-fcm-server-key-here');
define('FCM_SENDER_ID', 'your-fcm-sender-id-here');

// Create uploads directory if it doesn't exist
if (!file_exists(UPLOAD_PATH)) {
    mkdir(UPLOAD_PATH, 0755, true);
}

// Error reporting (disable in production)
if (defined('DEVELOPMENT') && DEVELOPMENT) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Set timezone
date_default_timezone_set('UTC');

// Start output buffering
ob_start();
?>
